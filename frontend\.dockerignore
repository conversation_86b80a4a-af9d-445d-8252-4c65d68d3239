# 依赖目录
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist
build

# 开发工具
.vscode
.idea
*.swp
*.swo

# 环境文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
logs
*.log

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
coverage
.nyc_output

# 依赖锁定文件（保留package-lock.json用于精确依赖）
yarn.lock

# 可选的npm缓存目录
.npm

# 可选的eslint缓存
.eslintcache

# 微服务
.serverless

# FuseBox缓存
.fusebox/

# DynamoDB本地
.dynamodb/

# TernJS端口文件
.tern-port

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git
.gitignore
README.md

# Docker
Dockerfile
.dockerignore

# 测试文件
**/*.test.js
**/*.test.ts
**/*.test.tsx
**/*.spec.js
**/*.spec.ts
**/*.spec.tsx

# 文档
docs/
*.md
