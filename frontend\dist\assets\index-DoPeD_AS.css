.ant-layout{min-height:100vh}.ant-layout-header{background:#001529;color:#fff;display:flex;align-items:center;justify-content:space-between}.ant-layout-content{padding:24px;background:#f0f2f5}:root{font-family:Inter,system-ui,Avenir,Helvetica,Arial,sans-serif;line-height:1.5;font-weight:400;color-scheme:light dark;color:#ffffffde;background-color:#242424;font-synthesis:none;text-rendering:optimizeLegibility;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;-webkit-text-size-adjust:100%}*{margin:0;padding:0;box-sizing:border-box}body{margin:0;min-width:320px;min-height:100vh;font-family:-apple-system,BlinkMacSystemFont,Segoe UI,PingFang SC,Hiragino Sans GB,Microsoft YaHei,Helvetica Neue,Helvetica,Arial,sans-serif}#root{width:100%;margin:0;padding:0;text-align:left}*{box-sizing:border-box}body{font-family:Roboto,Helvetica,Arial,sans-serif;margin:0;padding:0;background-color:var(--md-sys-color-background);color:var(--md-sys-color-on-background);transition:background-color .3s cubic-bezier(.4,0,.2,1),color .3s cubic-bezier(.4,0,.2,1)}.md3-surface{background-color:var(--md-sys-color-surface);color:var(--md-sys-color-on-surface);border-radius:12px;transition:all .3s cubic-bezier(.4,0,.2,1)}.md3-surface-container{background-color:var(--md-sys-color-surface-container);color:var(--md-sys-color-on-surface)}.md3-surface-container-low{background-color:var(--md-sys-color-surface-container-low);color:var(--md-sys-color-on-surface)}.md3-surface-container-high{background-color:var(--md-sys-color-surface-container-high);color:var(--md-sys-color-on-surface)}.md3-card{background-color:var(--md-sys-color-surface-container-low);border-radius:12px;box-shadow:0 1px 3px #0000001f,0 1px 2px #0000003d;transition:all .3s cubic-bezier(.4,0,.2,1);border:1px solid var(--md-sys-color-outline-variant)}.md3-card:hover{box-shadow:0 3px 6px #00000029,0 3px 6px #0000003b;transform:translateY(-1px)}[data-theme=dark] .md3-card{box-shadow:0 1px 3px #0000003d,0 1px 2px #0000007a}[data-theme=dark] .md3-card:hover{box-shadow:0 3px 6px #00000052,0 3px 6px #00000075}.md3-button{border-radius:20px;font-weight:500;font-size:14px;padding:10px 24px;border:none;cursor:pointer;transition:all .3s cubic-bezier(.4,0,.2,1);position:relative;overflow:hidden}.md3-button-filled{background-color:var(--md-sys-color-primary);color:var(--md-sys-color-on-primary)}.md3-button-filled:hover{box-shadow:0 2px 4px #0003;transform:translateY(-1px)}.md3-button-outlined{background-color:transparent;color:var(--md-sys-color-primary);border:1px solid var(--md-sys-color-outline)}.md3-button-text{background-color:transparent;color:var(--md-sys-color-primary)}.md3-ripple{position:relative;overflow:hidden}.md3-ripple:before{content:"";position:absolute;top:50%;left:50%;width:0;height:0;border-radius:50%;background:#ffffff4d;transform:translate(-50%,-50%);transition:width .6s,height .6s}.md3-ripple:active:before{width:300px;height:300px}.stock-bullish{color:var(--md-sys-color-bullish)!important}.stock-bearish{color:var(--md-sys-color-bearish)!important}.stock-neutral{color:var(--md-sys-color-neutral)!important}.stock-bullish-bg{background-color:var(--md-sys-color-success-container)!important;color:var(--md-sys-color-on-success-container)!important}.stock-bearish-bg{background-color:var(--md-sys-color-error-container)!important;color:var(--md-sys-color-on-error-container)!important}.md3-spacing-xs{margin:4px}.md3-spacing-sm{margin:8px}.md3-spacing-md{margin:16px}.md3-spacing-lg{margin:24px}.md3-spacing-xl{margin:32px}.md3-padding-xs{padding:4px}.md3-padding-sm{padding:8px}.md3-padding-md{padding:16px}.md3-padding-lg{padding:24px}.md3-padding-xl{padding:32px}.md3-margin-top-xs{margin-top:4px}.md3-margin-top-sm{margin-top:8px}.md3-margin-top-md{margin-top:16px}.md3-margin-top-lg{margin-top:24px}.md3-margin-top-xl{margin-top:32px}.md3-margin-bottom-xs{margin-bottom:4px}.md3-margin-bottom-sm{margin-bottom:8px}.md3-margin-bottom-md{margin-bottom:16px}.md3-margin-bottom-lg{margin-bottom:24px}.md3-margin-bottom-xl{margin-bottom:32px}.md3-margin-left-xs{margin-left:4px}.md3-margin-left-sm{margin-left:8px}.md3-margin-left-md{margin-left:16px}.md3-margin-left-lg{margin-left:24px}.md3-margin-left-xl{margin-left:32px}.md3-margin-right-xs{margin-right:4px}.md3-margin-right-sm{margin-right:8px}.md3-margin-right-md{margin-right:16px}.md3-margin-right-lg{margin-right:24px}.md3-margin-right-xl{margin-right:32px}.md3-headline-large{font-size:32px;font-weight:400;line-height:40px;letter-spacing:0px}.md3-headline-medium{font-size:28px;font-weight:400;line-height:36px;letter-spacing:0px}.md3-headline-small{font-size:24px;font-weight:400;line-height:32px;letter-spacing:0px}.md3-title-large{font-size:22px;font-weight:400;line-height:28px;letter-spacing:0px}.md3-title-medium{font-size:16px;font-weight:500;line-height:24px;letter-spacing:.15px}.md3-title-small{font-size:14px;font-weight:500;line-height:20px;letter-spacing:.1px}.md3-body-large{font-size:16px;font-weight:400;line-height:24px;letter-spacing:.5px}.md3-body-medium{font-size:14px;font-weight:400;line-height:20px;letter-spacing:.25px}.md3-body-small{font-size:12px;font-weight:400;line-height:16px;letter-spacing:.4px}.md3-label-large{font-size:14px;font-weight:500;line-height:20px;letter-spacing:.1px}.md3-label-medium{font-size:12px;font-weight:500;line-height:16px;letter-spacing:.5px}.md3-label-small{font-size:11px;font-weight:500;line-height:16px;letter-spacing:.5px}@media (max-width: 768px){.md3-padding-lg{padding:16px}.md3-padding-xl{padding:24px}.md3-margin-lg{margin:16px}.md3-margin-xl{margin:24px}.ant-layout-header>div{padding:0 16px!important}.ant-layout-content>div{padding:16px!important}.ant-table{font-size:12px}.ant-table-thead>tr>th{padding:8px 4px}.ant-table-tbody>tr>td{padding:8px 4px}.ant-input-search{font-size:14px}.ant-card{margin-bottom:16px}.ant-card-body{padding:16px}}@media (min-width: 769px) and (max-width: 1024px){.ant-layout-header>div{padding:0 20px!important}.ant-layout-content>div{padding:20px!important}}@media (min-width: 1400px){.ant-layout-header>div{max-width:1400px}.ant-layout-content>div{max-width:1400px}}.md3-transition-fast{transition:all .1s cubic-bezier(.4,0,.2,1)}.md3-transition-medium{transition:all .2s cubic-bezier(.4,0,.2,1)}.md3-transition-slow{transition:all .3s cubic-bezier(.4,0,.2,1)}.md3-focus-ring:focus{outline:2px solid var(--md-sys-color-primary);outline-offset:2px}.md3-disabled{opacity:.38;pointer-events:none}@keyframes ripple{0%{transform:scale(0);opacity:.6}to{transform:scale(1);opacity:0}}@keyframes pulse{0%{transform:scale(1);opacity:1}50%{transform:scale(1.05);opacity:.8}to{transform:scale(1);opacity:1}}@keyframes shake{0%,to{transform:translate(0)}10%,30%,50%,70%,90%{transform:translate(-2px)}20%,40%,60%,80%{transform:translate(2px)}}@keyframes fadeInUp{0%{opacity:0;transform:translateY(20px)}to{opacity:1;transform:translateY(0)}}@keyframes slideInLeft{0%{opacity:0;transform:translate(-20px)}to{opacity:1;transform:translate(0)}}@keyframes scaleIn{0%{opacity:0;transform:scale(.8)}to{opacity:1;transform:scale(1)}}.md3-animate-ripple{animation:ripple .6s ease-out}.md3-animate-pulse{animation:pulse 2s infinite}.md3-animate-shake{animation:shake .5s ease-in-out}.md3-animate-fade-in-up{animation:fadeInUp .3s ease-out}.md3-animate-slide-in-left{animation:slideInLeft .3s ease-out}.md3-animate-scale-in{animation:scaleIn .2s ease-out}.md3-card:hover{transform:translateY(-2px);box-shadow:0 6px 12px #00000026,0 4px 8px #0000001a}.md3-button:hover{transform:translateY(-1px)}.md3-button:active{transform:translateY(0) scale(.98)}.md3-loading{position:relative;overflow:hidden}.md3-loading:after{content:"";position:absolute;top:0;left:-100%;width:100%;height:100%;background:linear-gradient(90deg,transparent,rgba(255,255,255,.2),transparent);animation:loading-shimmer 1.5s infinite}@keyframes loading-shimmer{0%{left:-100%}to{left:100%}}.md3-state-layer{position:absolute;top:0;left:0;right:0;bottom:0;border-radius:inherit;opacity:0;transition:opacity .2s cubic-bezier(.4,0,.2,1);pointer-events:none}.md3-ripple:hover .md3-state-layer{opacity:.08;background-color:var(--md-sys-color-on-surface)}.md3-ripple:focus .md3-state-layer{opacity:.12;background-color:var(--md-sys-color-on-surface)}.md3-ripple:active .md3-state-layer{opacity:.16;background-color:var(--md-sys-color-on-surface)}::-webkit-scrollbar{width:8px;height:8px}::-webkit-scrollbar-track{background:var(--md-sys-color-surface-variant);border-radius:4px}::-webkit-scrollbar-thumb{background:var(--md-sys-color-outline);border-radius:4px;transition:background-color .2s ease}::-webkit-scrollbar-thumb:hover{background:var(--md-sys-color-on-surface-variant)}::selection{background-color:var(--md-sys-color-primary-container);color:var(--md-sys-color-on-primary-container)}
