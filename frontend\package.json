{"name": "stock-analysis-frontend", "version": "1.0.0", "description": "股票市场复盘分析系统前端", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:prod": "vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@types/react-router-dom": "^5.3.3", "antd": "^5.12.8", "axios": "^1.6.2", "dayjs": "^1.11.10", "echarts": "^5.4.3", "echarts-for-react": "^3.0.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^7.6.3", "react-window": "^1.8.11", "react-window-infinite-loader": "^1.0.10"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}